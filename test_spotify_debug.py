#!/usr/bin/env python3
"""
Debug script to test Spotify URL processing and identify specific issues.
"""

import asyncio
import sys
import re

def test_spotify_url_formats():
    """Test different Spotify URL formats."""
    print("=== Testing Spotify URL Formats ===")
    
    test_urls = [
        "https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh",
        "https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh?si=abc123",
        "https://open.spotify.com/playlist/37i9dQZF1DXcBWIGoYBM5M",
        "https://open.spotify.com/album/1DFixLWuPkv3KT3TnV35m3",
        "https://open.spotify.com/artist/1Xyo4u8uXC1ZmMpatF05PJ",
        "invalid-url",
        "https://youtube.com/watch?v=abc123"
    ]
    
    def extract_track_id(url):
        """Extract track ID from Spotify URL."""
        try:
            # Match track URLs
            track_match = re.search(r'/track/([a-zA-Z0-9]+)', url)
            if track_match:
                return track_match.group(1)
            return None
        except Exception as e:
            print(f"Error extracting track ID: {e}")
            return None
    
    for url in test_urls:
        print(f"\nTesting URL: {url}")
        
        # Check if it's a Spotify URL
        if 'spotify.com' not in url.lower():
            print("  ❌ Not a Spotify URL")
            continue
            
        # Extract track ID
        track_id = extract_track_id(url)
        if track_id:
            print(f"  ✅ Valid track URL, ID: {track_id}")
        else:
            # Check what type of Spotify URL it is
            if '/playlist/' in url:
                print("  ⚠️ Playlist URL (not supported)")
            elif '/album/' in url:
                print("  ⚠️ Album URL (not supported)")
            elif '/artist/' in url:
                print("  ⚠️ Artist URL (not supported)")
            else:
                print("  ❌ Unknown Spotify URL format")

async def test_spotify_info_extraction():
    """Test the bot's Spotify info extraction."""
    print("\n=== Testing Bot Spotify Info Extraction ===")
    
    try:
        sys.path.insert(0, '.')
        from bot import TelegramYTDLBot
        
        bot = TelegramYTDLBot()
        
        # Test with a valid Spotify track URL
        test_url = "https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh"
        
        print(f"Testing URL: {test_url}")
        
        try:
            info = await bot.extract_spotify_info_for_display(test_url)
            print(f"✅ Info extracted successfully: {info}")
            return True
        except Exception as e:
            print(f"❌ Info extraction failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Bot initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def test_spotdl_command():
    """Test if spotdl command works with a simple URL."""
    print("\n=== Testing spotdl Command ===")
    
    try:
        import subprocess
        
        # Test spotdl version
        result = subprocess.run(["spotdl", "--version"], capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print(f"✅ spotdl version: {result.stdout.strip()}")
        else:
            print(f"❌ spotdl version check failed: {result.stderr}")
            return False
        
        # Test a simple spotdl command (dry run)
        test_url = "https://open.spotify.com/track/4iV5W9uYEdYUVa79Axb7Rh"
        result = subprocess.run(
            ["spotdl", "--dry-run", test_url], 
            capture_output=True, text=True, timeout=30
        )
        
        if result.returncode == 0:
            print("✅ spotdl dry run successful")
            print(f"Output: {result.stdout[:200]}...")
            return True
        else:
            print(f"❌ spotdl dry run failed: {result.stderr}")
            return False
            
    except FileNotFoundError:
        print("❌ spotdl not found")
        return False
    except subprocess.TimeoutExpired:
        print("❌ spotdl command timed out")
        return False
    except Exception as e:
        print(f"❌ Error testing spotdl: {e}")
        return False

async def main():
    """Run all debug tests."""
    print("🔍 Debugging Spotify Download Issues")
    print("=" * 50)
    
    # Test URL formats
    test_spotify_url_formats()
    
    # Test bot info extraction
    info_result = await test_spotify_info_extraction()
    
    # Test spotdl command
    spotdl_result = await test_spotdl_command()
    
    print("\n" + "=" * 50)
    print("📊 Debug Results:")
    print(f"  Info Extraction: {'✅ PASS' if info_result else '❌ FAIL'}")
    print(f"  spotdl Command: {'✅ PASS' if spotdl_result else '❌ FAIL'}")
    
    if not info_result:
        print("\n🔍 Info extraction is failing - this is likely the main issue")
        print("The bot can't extract Spotify track information, so it fails before even trying spotdl")
    elif not spotdl_result:
        print("\n🔍 spotdl command is failing - this could be the issue")
        print("The bot can extract info but spotdl itself is not working properly")
    else:
        print("\n🤔 Both tests passed - the issue might be elsewhere")
        print("Check the actual error logs for more specific information")
    
    print("\n💡 Next Steps:")
    print("1. Check the debug logs for the specific error message")
    print("2. Try with a different Spotify track URL")
    print("3. Verify the Spotify URL format is correct")

if __name__ == "__main__":
    asyncio.run(main())
