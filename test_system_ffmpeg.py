#!/usr/bin/env python3
"""
Quick test to verify system FFmpeg detection fixes.
"""

import asyncio
import sys
import subprocess

def test_system_ffmpeg():
    """Test if system FFmpeg is available."""
    print("=== Testing System FFmpeg Detection ===")
    
    try:
        result = subprocess.run(["ffmpeg", "-version"], capture_output=True, text=True)
        if result.returncode == 0:
            version_line = result.stdout.split('\n')[0]
            print(f"✅ System FFmpeg detected: {version_line}")
            return True
        else:
            print("❌ FFmpeg test failed")
            return False
    except FileNotFoundError:
        print("❌ FFmpeg not found in system PATH")
        return False
    except Exception as e:
        print(f"❌ Error testing FFmpeg: {e}")
        return False

async def test_bot_ffmpeg_setup():
    """Test the bot's FFmpeg setup with the new system detection."""
    print("\n=== Testing Bot FFmpeg Setup ===")
    
    try:
        sys.path.insert(0, '.')
        from bot import download_ffmpeg
        
        result = await download_ffmpeg()
        
        if result == "system":
            print("✅ Bot correctly detected system FFmpeg")
            return True
        elif result:
            print(f"✅ Bot set up local FFmpeg: {result}")
            return True
        else:
            print("⚠️ Bot found no FFmpeg (will use limited functionality)")
            return True
            
    except Exception as e:
        print(f"❌ Bot FFmpeg setup failed: {e}")
        return False

async def test_bot_initialization():
    """Test bot initialization with FFmpeg setup."""
    print("\n=== Testing Bot Initialization ===")
    
    try:
        sys.path.insert(0, '.')
        from bot import TelegramYTDLBot
        
        # Create bot instance
        bot = TelegramYTDLBot()
        
        # Test FFmpeg setup
        ffmpeg_result = await bot.setup_ffmpeg()
        
        if ffmpeg_result == "system":
            print("✅ Bot initialized with system FFmpeg")
            print(f"Bot FFmpeg path: {bot.ffmpeg_path}")
            return True
        elif ffmpeg_result:
            print(f"✅ Bot initialized with local FFmpeg: {ffmpeg_result}")
            print(f"Bot FFmpeg path: {bot.ffmpeg_path}")
            return True
        else:
            print("⚠️ Bot initialized without FFmpeg (limited functionality)")
            print(f"Bot FFmpeg path: {bot.ffmpeg_path}")
            return True
            
    except Exception as e:
        print(f"❌ Bot initialization failed: {e}")
        import traceback
        traceback.print_exc()
        return False

async def main():
    """Run all tests."""
    print("🧪 Testing System FFmpeg Detection Fixes")
    print("=" * 50)
    
    tests = [
        ("System FFmpeg", test_system_ffmpeg),
        ("Bot FFmpeg Setup", test_bot_ffmpeg_setup),
        ("Bot Initialization", test_bot_initialization),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} crashed: {e}")
            results.append((test_name, False))
    
    print("\n" + "=" * 50)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {status} {test_name}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("\n🚀 System FFmpeg detection is working correctly!")
        print("Next deployment should show:")
        print("  ✅ 'Using system FFmpeg for yt-dlp'")
        print("  ✅ 'Using system FFmpeg for spotdl'")
        print("  ✅ No more 'No FFmpeg path available' warnings")
    else:
        print("\n⚠️ Some issues detected, but basic functionality should work")

if __name__ == "__main__":
    asyncio.run(main())
